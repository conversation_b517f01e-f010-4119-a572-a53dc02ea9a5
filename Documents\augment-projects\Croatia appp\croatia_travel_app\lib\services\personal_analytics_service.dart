import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/personal_analytics.dart';
import '../models/diary_entry.dart';

/// 📊 PERSONAL ANALYTICS SERVICE - Osobní analytický dashboard
class PersonalAnalyticsService {
  static final PersonalAnalyticsService _instance = PersonalAnalyticsService._internal();
  factory PersonalAnalyticsService() => _instance;
  PersonalAnalyticsService._internal();

  bool _isInitialized = false;
  final List<DiaryEntry> _userEntries = [];
  final Map<String, AnalyticsData> _analyticsCache = {};
  Timer? _analyticsTimer;

  /// Inicializace služby
  Future<void> initialize(List<DiaryEntry> userEntries) async {
    if (_isInitialized) return;

    try {
      debugPrint('📊 Inicializuji Personal Analytics Service...');
      
      _userEntries.clear();
      _userEntries.addAll(userEntries);
      
      await _loadAnalyticsData();
      _startAnalyticsTimer();
      
      _isInitialized = true;
      debugPrint('✅ Personal Analytics Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Personal Analytics: $e');
      _isInitialized = true;
    }
  }

  /// Získání kompletního analytického dashboardu
  Future<PersonalDashboard> getPersonalDashboard({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final now = DateTime.now();
      final from = fromDate ?? now.subtract(const Duration(days: 30));
      final to = toDate ?? now;

      // Filtrování záznamů podle data
      final filteredEntries = _userEntries.where((entry) =>
        entry.date.isAfter(from.subtract(const Duration(days: 1))) &&
        entry.date.isBefore(to.add(const Duration(days: 1)))
      ).toList();

      // Generování všech analytických dat
      final writingStats = await _generateWritingStatistics(filteredEntries);
      final moodAnalysis = await _generateMoodAnalysis(filteredEntries);
      final locationInsights = await _generateLocationInsights(filteredEntries);
      final timePatterns = await _generateTimePatterns(filteredEntries);
      final contentAnalysis = await _generateContentAnalysis(filteredEntries);
      final progressMetrics = await _generateProgressMetrics(filteredEntries);
      final achievements = await _generateAchievements(filteredEntries);
      final insights = await _generatePersonalInsights(filteredEntries);

      return PersonalDashboard(
        userId: 'current_user',
        periodStart: from,
        periodEnd: to,
        writingStatistics: writingStats,
        moodAnalysis: moodAnalysis,
        locationInsights: locationInsights,
        timePatterns: timePatterns,
        contentAnalysis: contentAnalysis,
        progressMetrics: progressMetrics,
        achievements: achievements,
        personalInsights: insights,
        generatedAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('❌ Chyba při generování dashboardu: $e');
      return PersonalDashboard.empty();
    }
  }

  /// Generování statistik psaní
  Future<WritingStatistics> _generateWritingStatistics(List<DiaryEntry> entries) async {
    if (entries.isEmpty) return WritingStatistics.empty();

    final totalEntries = entries.length;
    final totalWords = entries.fold<int>(0, (sum, entry) => sum + entry.wordCount);
    final totalCharacters = entries.fold<int>(0, (sum, entry) => sum + entry.content.length);
    
    final averageWordsPerEntry = totalWords / totalEntries;
    final averageCharactersPerEntry = totalCharacters / totalEntries;
    
    // Analýza frekvence psaní
    final daysSinceFirst = entries.isNotEmpty 
        ? DateTime.now().difference(entries.last.date).inDays + 1
        : 1;
    final writingFrequency = totalEntries / daysSinceFirst;
    
    // Nejdelší a nejkratší zápis
    final longestEntry = entries.reduce((a, b) => a.wordCount > b.wordCount ? a : b);
    final shortestEntry = entries.reduce((a, b) => a.wordCount < b.wordCount ? a : b);
    
    // Streak analýza
    final currentStreak = _calculateCurrentStreak(entries);
    final longestStreak = _calculateLongestStreak(entries);
    
    // Týdenní statistiky
    final weeklyStats = _calculateWeeklyStats(entries);
    
    return WritingStatistics(
      totalEntries: totalEntries,
      totalWords: totalWords,
      totalCharacters: totalCharacters,
      averageWordsPerEntry: averageWordsPerEntry,
      averageCharactersPerEntry: averageCharactersPerEntry,
      writingFrequency: writingFrequency,
      longestEntry: longestEntry,
      shortestEntry: shortestEntry,
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      weeklyStats: weeklyStats,
    );
  }

  /// Generování analýzy nálad
  Future<MoodAnalysis> _generateMoodAnalysis(List<DiaryEntry> entries) async {
    final moodCounts = <DiaryMood, int>{};
    final moodTrends = <DateTime, DiaryMood>{};
    
    for (final entry in entries) {
      if (entry.mood != null) {
        moodCounts[entry.mood!] = (moodCounts[entry.mood!] ?? 0) + 1;
        moodTrends[entry.date] = entry.mood!;
      }
    }
    
    // Dominantní nálada
    final dominantMood = moodCounts.isNotEmpty
        ? moodCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : null;
    
    // Průměrné skóre nálady
    final averageMoodScore = _calculateAverageMoodScore(entries);
    
    // Trendy nálad
    final moodTrendAnalysis = _analyzeMoodTrends(moodTrends);
    
    // Nejlepší a nejhorší dny
    final bestDays = _findBestMoodDays(entries);
    final worstDays = _findWorstMoodDays(entries);
    
    return MoodAnalysis(
      moodDistribution: moodCounts,
      dominantMood: dominantMood,
      averageMoodScore: averageMoodScore,
      moodTrends: moodTrends,
      trendAnalysis: moodTrendAnalysis,
      bestDays: bestDays,
      worstDays: worstDays,
      moodStability: _calculateMoodStability(entries),
    );
  }

  /// Generování lokačních insights
  Future<LocationInsights> _generateLocationInsights(List<DiaryEntry> entries) async {
    final locationCounts = <String, int>{};
    final locationMoods = <String, List<DiaryMood>>{};
    
    for (final entry in entries) {
      if (entry.location != null && entry.location!.isNotEmpty) {
        final location = entry.location!;
        locationCounts[location] = (locationCounts[location] ?? 0) + 1;
        
        if (entry.mood != null) {
          locationMoods.putIfAbsent(location, () => []).add(entry.mood!);
        }
      }
    }
    
    // Nejnavštěvovanější místa
    final topLocations = locationCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Nejšťastnější místa
    final happiestLocations = _findHappiestLocations(locationMoods);
    
    // Unikátní lokace
    final uniqueLocations = locationCounts.keys.length;
    
    return LocationInsights(
      topLocations: topLocations.take(10).toList(),
      happiestLocations: happiestLocations,
      uniqueLocations: uniqueLocations,
      locationMoodMap: locationMoods,
      travelPatterns: _analyzeTravelPatterns(entries),
    );
  }

  /// Generování časových vzorců
  Future<TimePatterns> _generateTimePatterns(List<DiaryEntry> entries) async {
    final hourCounts = <int, int>{};
    final dayOfWeekCounts = <int, int>{};
    final monthCounts = <int, int>{};
    
    for (final entry in entries) {
      final hour = entry.date.hour;
      final dayOfWeek = entry.date.weekday;
      final month = entry.date.month;
      
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
      dayOfWeekCounts[dayOfWeek] = (dayOfWeekCounts[dayOfWeek] ?? 0) + 1;
      monthCounts[month] = (monthCounts[month] ?? 0) + 1;
    }
    
    // Nejaktivnější hodina
    final peakHour = hourCounts.isNotEmpty
        ? hourCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : 12;
    
    // Nejaktivnější den v týdnu
    final peakDayOfWeek = dayOfWeekCounts.isNotEmpty
        ? dayOfWeekCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : 1;
    
    return TimePatterns(
      hourlyDistribution: hourCounts,
      dayOfWeekDistribution: dayOfWeekCounts,
      monthlyDistribution: monthCounts,
      peakWritingHour: peakHour,
      peakWritingDay: peakDayOfWeek,
      writingConsistency: _calculateWritingConsistency(entries),
    );
  }

  /// Generování analýzy obsahu
  Future<ContentAnalysis> _generateContentAnalysis(List<DiaryEntry> entries) async {
    final allWords = <String>[];
    final topicCounts = <String, int>{};
    
    for (final entry in entries) {
      final words = entry.content.toLowerCase()
          .split(RegExp(r'\W+'))
          .where((word) => word.length > 3)
          .toList();
      
      allWords.addAll(words);
      
      // Analýza témat na základě tagů
      for (final tag in entry.tags) {
        topicCounts[tag] = (topicCounts[tag] ?? 0) + 1;
      }
    }
    
    // Nejčastější slova
    final wordFrequency = <String, int>{};
    for (final word in allWords) {
      wordFrequency[word] = (wordFrequency[word] ?? 0) + 1;
    }
    
    final topWords = wordFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Nejčastější témata
    final topTopics = topicCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return ContentAnalysis(
      topWords: topWords.take(20).toList(),
      topTopics: topTopics.take(10).toList(),
      vocabularySize: wordFrequency.keys.length,
      averageReadingTime: _calculateAverageReadingTime(entries),
      contentDiversity: _calculateContentDiversity(entries),
    );
  }

  /// Generování metrik pokroku
  Future<ProgressMetrics> _generateProgressMetrics(List<DiaryEntry> entries) async {
    final now = DateTime.now();
    final thisMonth = entries.where((e) => 
      e.date.year == now.year && e.date.month == now.month
    ).length;
    
    final lastMonth = entries.where((e) => {
      final lastMonthDate = DateTime(now.year, now.month - 1);
      return e.date.year == lastMonthDate.year && e.date.month == lastMonthDate.month;
    }).length;
    
    final growthRate = lastMonth > 0 ? (thisMonth - lastMonth) / lastMonth : 0.0;
    
    return ProgressMetrics(
      entriesThisMonth: thisMonth,
      entriesLastMonth: lastMonth,
      growthRate: growthRate,
      consistencyScore: _calculateConsistencyScore(entries),
      qualityScore: _calculateQualityScore(entries),
      engagementScore: _calculateEngagementScore(entries),
    );
  }

  /// Generování achievementů
  Future<List<Achievement>> _generateAchievements(List<DiaryEntry> entries) async {
    final achievements = <Achievement>[];
    
    // First Entry Achievement
    if (entries.isNotEmpty) {
      achievements.add(Achievement(
        id: 'first_entry',
        name: 'První krok',
        description: 'Napsal jste svůj první zápis do deníku',
        icon: '🌟',
        unlockedAt: entries.last.date,
        category: AchievementCategory.milestone,
        rarity: AchievementRarity.common,
      ));
    }
    
    // Streak Achievements
    final currentStreak = _calculateCurrentStreak(entries);
    if (currentStreak >= 7) {
      achievements.add(Achievement(
        id: 'week_streak',
        name: 'Týdenní série',
        description: 'Psal jste 7 dní v řadě',
        icon: '🔥',
        unlockedAt: DateTime.now(),
        category: AchievementCategory.consistency,
        rarity: AchievementRarity.uncommon,
      ));
    }
    
    // Word Count Achievements
    final totalWords = entries.fold<int>(0, (sum, entry) => sum + entry.wordCount);
    if (totalWords >= 10000) {
      achievements.add(Achievement(
        id: 'wordsmith',
        name: 'Mistr slov',
        description: 'Napsal jste více než 10,000 slov',
        icon: '📝',
        unlockedAt: DateTime.now(),
        category: AchievementCategory.content,
        rarity: AchievementRarity.rare,
      ));
    }
    
    return achievements;
  }

  /// Generování osobních insights
  Future<List<PersonalInsight>> _generatePersonalInsights(List<DiaryEntry> entries) async {
    final insights = <PersonalInsight>[];
    
    // Mood Insight
    final moodAnalysis = await _generateMoodAnalysis(entries);
    if (moodAnalysis.dominantMood != null) {
      insights.add(PersonalInsight(
        id: 'dominant_mood',
        title: 'Vaše dominantní nálada',
        description: 'Nejčastěji se cítíte ${moodAnalysis.dominantMood!.displayName.toLowerCase()}',
        type: InsightType.mood,
        importance: InsightImportance.high,
        actionable: true,
        recommendation: 'Zkuste analyzovat, co přispívá k této náladě',
      ));
    }
    
    // Writing Pattern Insight
    final timePatterns = await _generateTimePatterns(entries);
    insights.add(PersonalInsight(
      id: 'writing_time',
      title: 'Váš nejproduktivnější čas',
      description: 'Nejčastěji píšete kolem ${timePatterns.peakWritingHour}:00',
      type: InsightType.pattern,
      importance: InsightImportance.medium,
      actionable: true,
      recommendation: 'Naplánujte si psaní na tuto dobu pro lepší výsledky',
    ));
    
    return insights;
  }

  /// Pomocné metody pro výpočty
  int _calculateCurrentStreak(List<DiaryEntry> entries) {
    if (entries.isEmpty) return 0;
    
    final sortedEntries = List<DiaryEntry>.from(entries)
      ..sort((a, b) => b.date.compareTo(a.date));
    
    int streak = 0;
    DateTime? lastDate;
    
    for (final entry in sortedEntries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      
      if (lastDate == null) {
        lastDate = entryDate;
        streak = 1;
      } else {
        final daysDiff = lastDate.difference(entryDate).inDays;
        if (daysDiff == 1) {
          streak++;
          lastDate = entryDate;
        } else {
          break;
        }
      }
    }
    
    return streak;
  }

  int _calculateLongestStreak(List<DiaryEntry> entries) {
    if (entries.isEmpty) return 0;
    
    final sortedEntries = List<DiaryEntry>.from(entries)
      ..sort((a, b) => a.date.compareTo(b.date));
    
    int maxStreak = 0;
    int currentStreak = 0;
    DateTime? lastDate;
    
    for (final entry in sortedEntries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      
      if (lastDate == null) {
        currentStreak = 1;
      } else {
        final daysDiff = entryDate.difference(lastDate).inDays;
        if (daysDiff == 1) {
          currentStreak++;
        } else {
          maxStreak = max(maxStreak, currentStreak);
          currentStreak = 1;
        }
      }
      
      lastDate = entryDate;
    }
    
    return max(maxStreak, currentStreak);
  }

  Map<int, int> _calculateWeeklyStats(List<DiaryEntry> entries) {
    final weeklyStats = <int, int>{};
    
    for (final entry in entries) {
      final weekNumber = _getWeekNumber(entry.date);
      weeklyStats[weekNumber] = (weeklyStats[weekNumber] ?? 0) + 1;
    }
    
    return weeklyStats;
  }

  int _getWeekNumber(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final daysSinceStart = date.difference(startOfYear).inDays;
    return (daysSinceStart / 7).floor() + 1;
  }

  double _calculateAverageMoodScore(List<DiaryEntry> entries) {
    final moodScores = entries
        .where((e) => e.mood != null)
        .map((e) => e.mood!.score)
        .toList();
    
    return moodScores.isNotEmpty 
        ? moodScores.reduce((a, b) => a + b) / moodScores.length
        : 0.0;
  }

  String _analyzeMoodTrends(Map<DateTime, DiaryMood> moodTrends) {
    if (moodTrends.length < 2) return 'Nedostatek dat pro analýzu trendu';
    
    final sortedDates = moodTrends.keys.toList()..sort();
    final recentMoods = sortedDates.takeLast(7)
        .map((date) => moodTrends[date]!.score)
        .toList();
    
    if (recentMoods.length < 2) return 'Nedostatek dat';
    
    final trend = recentMoods.last - recentMoods.first;
    
    if (trend > 0.5) return 'Zlepšující se nálada';
    if (trend < -0.5) return 'Zhoršující se nálada';
    return 'Stabilní nálada';
  }

  List<DateTime> _findBestMoodDays(List<DiaryEntry> entries) {
    return entries
        .where((e) => e.mood != null && e.mood!.score >= 4.0)
        .map((e) => e.date)
        .toList();
  }

  List<DateTime> _findWorstMoodDays(List<DiaryEntry> entries) {
    return entries
        .where((e) => e.mood != null && e.mood!.score <= 2.0)
        .map((e) => e.date)
        .toList();
  }

  double _calculateMoodStability(List<DiaryEntry> entries) {
    final moodScores = entries
        .where((e) => e.mood != null)
        .map((e) => e.mood!.score)
        .toList();
    
    if (moodScores.length < 2) return 1.0;
    
    final mean = moodScores.reduce((a, b) => a + b) / moodScores.length;
    final variance = moodScores
        .map((score) => pow(score - mean, 2))
        .reduce((a, b) => a + b) / moodScores.length;
    
    return 1.0 / (1.0 + variance); // Vyšší hodnota = stabilnější nálada
  }

  List<MapEntry<String, double>> _findHappiestLocations(Map<String, List<DiaryMood>> locationMoods) {
    final locationHappiness = <String, double>{};
    
    for (final entry in locationMoods.entries) {
      final averageScore = entry.value
          .map((mood) => mood.score)
          .reduce((a, b) => a + b) / entry.value.length;
      locationHappiness[entry.key] = averageScore;
    }
    
    final sorted = locationHappiness.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sorted.take(5).toList();
  }

  List<String> _analyzeTravelPatterns(List<DiaryEntry> entries) {
    final patterns = <String>[];
    
    final locationsWithDates = entries
        .where((e) => e.location != null)
        .map((e) => MapEntry(e.location!, e.date))
        .toList();
    
    if (locationsWithDates.length >= 3) {
      patterns.add('Cestujete pravidelně');
    }
    
    final uniqueLocations = locationsWithDates.map((e) => e.key).toSet().length;
    if (uniqueLocations > 10) {
      patterns.add('Objevujete nová místa');
    }
    
    return patterns;
  }

  double _calculateWritingConsistency(List<DiaryEntry> entries) {
    if (entries.length < 7) return 0.0;
    
    final sortedEntries = List<DiaryEntry>.from(entries)
      ..sort((a, b) => a.date.compareTo(b.date));
    
    final intervals = <int>[];
    for (int i = 1; i < sortedEntries.length; i++) {
      final interval = sortedEntries[i].date.difference(sortedEntries[i-1].date).inDays;
      intervals.add(interval);
    }
    
    final averageInterval = intervals.reduce((a, b) => a + b) / intervals.length;
    final variance = intervals
        .map((interval) => pow(interval - averageInterval, 2))
        .reduce((a, b) => a + b) / intervals.length;
    
    return 1.0 / (1.0 + variance); // Vyšší hodnota = konzistentnější psaní
  }

  Duration _calculateAverageReadingTime(List<DiaryEntry> entries) {
    final averageWords = entries.isNotEmpty
        ? entries.fold<int>(0, (sum, entry) => sum + entry.wordCount) / entries.length
        : 0;
    
    // Průměrná rychlost čtení: 200 slov za minutu
    final readingTimeMinutes = averageWords / 200;
    return Duration(minutes: readingTimeMinutes.round());
  }

  double _calculateContentDiversity(List<DiaryEntry> entries) {
    final allTags = entries.expand((e) => e.tags).toSet();
    final totalEntries = entries.length;
    
    return totalEntries > 0 ? allTags.length / totalEntries : 0.0;
  }

  double _calculateConsistencyScore(List<DiaryEntry> entries) {
    return _calculateWritingConsistency(entries);
  }

  double _calculateQualityScore(List<DiaryEntry> entries) {
    if (entries.isEmpty) return 0.0;
    
    final averageWordCount = entries.fold<int>(0, (sum, entry) => sum + entry.wordCount) / entries.length;
    final averageTagCount = entries.fold<int>(0, (sum, entry) => sum + entry.tags.length) / entries.length;
    
    // Normalizace na škálu 0-1
    final wordScore = min(averageWordCount / 200, 1.0); // 200 slov = plné skóre
    final tagScore = min(averageTagCount / 5, 1.0); // 5 tagů = plné skóre
    
    return (wordScore + tagScore) / 2;
  }

  double _calculateEngagementScore(List<DiaryEntry> entries) {
    if (entries.isEmpty) return 0.0;
    
    final entriesWithPhotos = entries.where((e) => e.hasPhotos).length;
    final entriesWithLocation = entries.where((e) => e.location != null).length;
    final entriesWithMood = entries.where((e) => e.mood != null).length;
    
    final photoScore = entriesWithPhotos / entries.length;
    final locationScore = entriesWithLocation / entries.length;
    final moodScore = entriesWithMood / entries.length;
    
    return (photoScore + locationScore + moodScore) / 3;
  }

  /// Načítání a ukládání dat
  Future<void> _loadAnalyticsData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final analyticsJson = prefs.getString('personal_analytics');
      
      if (analyticsJson != null) {
        final Map<String, dynamic> data = jsonDecode(analyticsJson);
        // Načtení cache dat
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání analytics dat: $e');
    }
  }

  void _startAnalyticsTimer() {
    _analyticsTimer?.cancel();
    _analyticsTimer = Timer.periodic(const Duration(hours: 1), (_) {
      // Pravidelná aktualizace analytics
      _updateAnalyticsCache();
    });
  }

  Future<void> _updateAnalyticsCache() async {
    // Aktualizace cache pro rychlejší přístup k datům
  }

  @override
  void dispose() {
    _analyticsTimer?.cancel();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  int get totalEntries => _userEntries.length;
}
