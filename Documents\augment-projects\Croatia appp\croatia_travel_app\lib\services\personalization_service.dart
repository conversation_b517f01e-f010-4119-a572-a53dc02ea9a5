import 'dart:async';
import 'package:flutter/foundation.dart';

import '../models/user_models.dart';

/// Služba pro personalizaci a doporučení
class PersonalizationService extends ChangeNotifier {
  static final PersonalizationService _instance =
      PersonalizationService._internal();
  factory PersonalizationService() => _instance;
  PersonalizationService._internal();

  UserProfile? _userProfile;
  List<PersonalizedRecommendation> _recommendations = [];
  List<TravelItinerary> _itineraries = [];
  Map<String, double> _userPreferences = {};
  bool _isInitialized = false;

  // Gettery
  UserProfile? get userProfile => _userProfile;
  List<PersonalizedRecommendation> get recommendations => _recommendations;
  List<TravelItinerary> get itineraries => _itineraries;
  Map<String, double> get userPreferences => _userPreferences;
  bool get isInitialized => _isInitialized;
  bool get hasProfile => _userProfile != null;

  /// Inicializuje personalizační službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadUserProfile();
      await _loadRecommendations();
      await _loadItineraries();
      await _loadUserPreferences();

      _isInitialized = true;
      debugPrint('Personalizační služba inicializována');
    } catch (e) {
      debugPrint('Chyba při inicializaci personalizační služby: $e');
      throw Exception('Nepodařilo se inicializovat personalizaci');
    }
  }

  /// Vytvoří nebo aktualizuje uživatelský profil
  Future<void> updateUserProfile({
    String? displayName,
    List<TravelType>? travelPreferences,
    AgeGroup? ageGroup,
    BudgetCategory? budgetCategory,
    List<String>? languages,
  }) async {
    try {
      final now = DateTime.now();

      if (_userProfile == null) {
        // Vytvoří nový profil
        _userProfile = UserProfile(
          id: now.millisecondsSinceEpoch.toString(),
          displayName: displayName,
          travelPreferences: travelPreferences ?? [],
          ageGroup: ageGroup,
          budgetCategory: budgetCategory,
          languages: languages ?? ['cs'],
          createdAt: now,
          lastActiveAt: now,
        );
      } else {
        // Aktualizuje existující profil
        _userProfile = _userProfile!.copyWith(
          displayName: displayName ?? _userProfile!.displayName,
          travelPreferences:
              travelPreferences ?? _userProfile!.travelPreferences,
          ageGroup: ageGroup ?? _userProfile!.ageGroup,
          budgetCategory: budgetCategory ?? _userProfile!.budgetCategory,
          languages: languages ?? _userProfile!.languages,
          lastActiveAt: now,
        );
      }

      await _saveUserProfile();
      await _generateRecommendations();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při aktualizaci profilu: $e');
      rethrow;
    }
  }

  /// Přidá místo do wishlistu
  Future<void> addToWishlist(String placeId) async {
    if (_userProfile == null) return;

    try {
      final updatedWishlist = List<String>.from(_userProfile!.wishlist);
      if (!updatedWishlist.contains(placeId)) {
        updatedWishlist.add(placeId);

        _userProfile = _userProfile!.copyWith(wishlist: updatedWishlist);
        await _saveUserProfile();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Chyba při přidávání do wishlistu: $e');
    }
  }

  /// Odebere místo z wishlistu
  Future<void> removeFromWishlist(String placeId) async {
    if (_userProfile == null) return;

    try {
      final updatedWishlist = List<String>.from(_userProfile!.wishlist);
      updatedWishlist.remove(placeId);

      _userProfile = _userProfile!.copyWith(wishlist: updatedWishlist);
      await _saveUserProfile();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při odebírání z wishlistu: $e');
    }
  }

  /// Označí místo jako navštívené
  Future<void> markAsVisited(String placeId) async {
    if (_userProfile == null) return;

    try {
      final updatedVisited = List<String>.from(_userProfile!.visitedPlaces);
      if (!updatedVisited.contains(placeId)) {
        updatedVisited.add(placeId);

        _userProfile = _userProfile!.copyWith(visitedPlaces: updatedVisited);
        await _saveUserProfile();

        // Aktualizuje preference na základě navštíveného místa
        await _updatePreferencesFromVisit(placeId);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Chyba při označování jako navštívené: $e');
    }
  }

  /// Generuje personalizovaná doporučení
  Future<void> _generateRecommendations() async {
    if (_userProfile == null) return;

    try {
      _recommendations.clear();

      // Generuje doporučení na základě preferencí
      final recommendations = await _createRecommendations();
      _recommendations.addAll(recommendations);

      await _saveRecommendations();
      debugPrint('Vygenerováno ${_recommendations.length} doporučení');
    } catch (e) {
      debugPrint('Chyba při generování doporučení: $e');
    }
  }

  /// Vytváří konkrétní doporučení
  Future<List<PersonalizedRecommendation>> _createRecommendations() async {
    final recommendations = <PersonalizedRecommendation>[];
    final preferences = _userProfile!.travelPreferences;
    final budget = _userProfile!.budgetCategory;

    // Doporučení podle typu cestování
    for (final preference in preferences) {
      recommendations.addAll(
        await _getRecommendationsForType(preference, budget),
      );
    }

    // Seřadí podle skóre
    recommendations.sort((a, b) => b.score.compareTo(a.score));

    // Vrátí top 20 doporučení
    return recommendations.take(20).toList();
  }

  /// Získá doporučení pro konkrétní typ cestování
  Future<List<PersonalizedRecommendation>> _getRecommendationsForType(
    TravelType type,
    BudgetCategory? budget,
  ) async {
    final recommendations = <PersonalizedRecommendation>[];

    switch (type) {
      case TravelType.culture:
        recommendations.addAll(_createCulturalRecommendations(budget));
        break;
      case TravelType.nature:
        recommendations.addAll(_createNatureRecommendations(budget));
        break;
      case TravelType.beach:
        recommendations.addAll(_createBeachRecommendations(budget));
        break;
      case TravelType.food:
        recommendations.addAll(_createFoodRecommendations(budget));
        break;
      case TravelType.adventure:
        recommendations.addAll(_createAdventureRecommendations(budget));
        break;
      default:
        break;
    }

    return recommendations;
  }

  /// Vytváří kulturní doporučení
  List<PersonalizedRecommendation> _createCulturalRecommendations(
    BudgetCategory? budget,
  ) {
    return [
      PersonalizedRecommendation(
        id: 'culture_dubrovnik',
        title: 'Hradby Dubrovníku',
        description:
            'Procházka po nejkrásnějších hradbách Evropy s úžasným výhledem na Jaderské moře.',
        type: 'place',
        score: 0.95,
        reasons: ['Milujete kulturu', 'UNESCO památka', 'Ikonické místo'],
        createdAt: DateTime.now(),
        data: {
          'location': 'Dubrovník',
          'price': budget == BudgetCategory.budget ? 15 : 25,
          'duration': '2-3 hodiny',
        },
      ),
      PersonalizedRecommendation(
        id: 'culture_split',
        title: 'Diokleciánův palác',
        description:
            'Antický palác přímo v centru Splitu, kde stále žijí lidé.',
        type: 'place',
        score: 0.90,
        reasons: ['Historické místo', 'Jedinečná architektura'],
        createdAt: DateTime.now(),
        data: {
          'location': 'Split',
          'price': 0, // zdarma
          'duration': '1-2 hodiny',
        },
      ),
    ];
  }

  /// Vytváří přírodní doporučení
  List<PersonalizedRecommendation> _createNatureRecommendations(
    BudgetCategory? budget,
  ) {
    return [
      PersonalizedRecommendation(
        id: 'nature_plitvice',
        title: 'Plitvická jezera',
        description:
            'Nejkrásnější národní park Chorvatska s kaskádami jezer a vodopádů.',
        type: 'place',
        score: 0.98,
        reasons: [
          'Milujete přírodu',
          'UNESCO přírodní památka',
          'Nezapomenutelný zážitek',
        ],
        createdAt: DateTime.now(),
        data: {
          'location': 'Plitvická jezera',
          'price': budget == BudgetCategory.budget ? 20 : 35,
          'duration': 'celý den',
        },
      ),
    ];
  }

  /// Vytváří plážová doporučení
  List<PersonalizedRecommendation> _createBeachRecommendations(
    BudgetCategory? budget,
  ) {
    return [
      PersonalizedRecommendation(
        id: 'beach_zlatni_rat',
        title: 'Zlatni Rat',
        description: 'Nejfotografovanější pláž Chorvatska na ostrově Brač.',
        type: 'place',
        score: 0.92,
        reasons: ['Milujete pláže', 'Jedinečný tvar', 'Křišťálově čistá voda'],
        createdAt: DateTime.now(),
        data: {
          'location': 'Bol, Brač',
          'price': 0, // zdarma
          'activities': ['plavání', 'windsurfing', 'opalování'],
        },
      ),
    ];
  }

  /// Vytváří gastronomická doporučení
  List<PersonalizedRecommendation> _createFoodRecommendations(
    BudgetCategory? budget,
  ) {
    return [
      PersonalizedRecommendation(
        id: 'food_konoba',
        title: 'Tradiční konoba',
        description:
            'Ochutnejte autentickou chorvatskou kuchyni v rodinné konobě.',
        type: 'restaurant',
        score: 0.88,
        reasons: [
          'Milujete gastronomii',
          'Místní speciality',
          'Autentický zážitek',
        ],
        createdAt: DateTime.now(),
        data: {
          'cuisine': 'chorvatská',
          'price_range': budget == BudgetCategory.budget ? '15-25€' : '25-40€',
          'specialties': ['čevapčiči', 'pašticada', 'crni rižot'],
        },
      ),
    ];
  }

  /// Vytváří dobrodružná doporučení
  List<PersonalizedRecommendation> _createAdventureRecommendations(
    BudgetCategory? budget,
  ) {
    return [
      PersonalizedRecommendation(
        id: 'adventure_rafting',
        title: 'Rafting na řece Cetině',
        description: 'Adrenalinový sjezd divoké řeky s úžasnými scenériemi.',
        type: 'activity',
        score: 0.85,
        reasons: ['Milujete dobrodružství', 'Adrenalin', 'Krásná příroda'],
        createdAt: DateTime.now(),
        data: {
          'location': 'Omiš',
          'price': budget == BudgetCategory.budget ? 35 : 55,
          'duration': '3-4 hodiny',
          'difficulty': 'střední',
        },
      ),
    ];
  }

  /// Aktualizuje preference na základě návštěvy
  Future<void> _updatePreferencesFromVisit(String placeId) async {
    // TODO: Implementovat machine learning pro aktualizaci preferencí
    // Na základě navštívených míst se učí, co uživatel preferuje
  }

  /// Vytvoří smart itinerář
  Future<TravelItinerary> createSmartItinerary({
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    List<String>? mustVisitPlaces,
  }) async {
    try {
      final itinerary = TravelItinerary(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        description: 'Personalizovaný itinerář vytvořený AI',
        startDate: startDate,
        endDate: endDate,
        isGenerated: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: Implementovat AI generování itineráře
      // Na základě preferencí, rozpočtu a dostupného času

      _itineraries.add(itinerary);
      await _saveItineraries();
      notifyListeners();

      return itinerary;
    } catch (e) {
      debugPrint('Chyba při vytváření itineráře: $e');
      rethrow;
    }
  }

  /// Označí doporučení jako zobrazené
  Future<void> markRecommendationAsViewed(String recommendationId) async {
    try {
      final index = _recommendations.indexWhere(
        (r) => r.id == recommendationId,
      );
      if (index != -1) {
        _recommendations[index] = _recommendations[index].copyWith(
          isViewed: true,
        );
        await _saveRecommendations();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Chyba při označování doporučení: $e');
    }
  }

  /// Označí doporučení jako oblíbené
  Future<void> toggleRecommendationLike(String recommendationId) async {
    try {
      final index = _recommendations.indexWhere(
        (r) => r.id == recommendationId,
      );
      if (index != -1) {
        final current = _recommendations[index];
        _recommendations[index] = current.copyWith(isLiked: !current.isLiked);
        await _saveRecommendations();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Chyba při označování oblíbeného: $e');
    }
  }

  /// Získá doporučení podle typu
  List<PersonalizedRecommendation> getRecommendationsByType(String type) {
    return _recommendations.where((r) => r.type == type).toList();
  }

  /// Získá nejlepší doporučení
  List<PersonalizedRecommendation> getTopRecommendations({int limit = 5}) {
    final sorted = List<PersonalizedRecommendation>.from(_recommendations);
    sorted.sort((a, b) => b.score.compareTo(a.score));
    return sorted.take(limit).toList();
  }

  /// Načte uživatelský profil
  Future<void> _loadUserProfile() async {
    try {
      // TODO: Implementovat načítání z SQLite/Firebase
      _userProfile = null;
    } catch (e) {
      debugPrint('Chyba při načítání profilu: $e');
    }
  }

  /// Uloží uživatelský profil
  Future<void> _saveUserProfile() async {
    try {
      // TODO: Implementovat ukládání do SQLite/Firebase
    } catch (e) {
      debugPrint('Chyba při ukládání profilu: $e');
    }
  }

  /// Načte doporučení
  Future<void> _loadRecommendations() async {
    try {
      // TODO: Implementovat načítání z SQLite/Firebase
      _recommendations = [];
    } catch (e) {
      debugPrint('Chyba při načítání doporučení: $e');
    }
  }

  /// Uloží doporučení
  Future<void> _saveRecommendations() async {
    try {
      // TODO: Implementovat ukládání do SQLite/Firebase
    } catch (e) {
      debugPrint('Chyba při ukládání doporučení: $e');
    }
  }

  /// Načte itineráře
  Future<void> _loadItineraries() async {
    try {
      // TODO: Implementovat načítání z SQLite/Firebase
      _itineraries = [];
    } catch (e) {
      debugPrint('Chyba při načítání itinerářů: $e');
    }
  }

  /// Uloží itineráře
  Future<void> _saveItineraries() async {
    try {
      // TODO: Implementovat ukládání do SQLite/Firebase
    } catch (e) {
      debugPrint('Chyba při ukládání itinerářů: $e');
    }
  }

  /// Načte uživatelské preference
  Future<void> _loadUserPreferences() async {
    try {
      // TODO: Implementovat načítání z SharedPreferences
      _userPreferences = {};
    } catch (e) {
      debugPrint('Chyba při načítání preferencí: $e');
    }
  }
}
