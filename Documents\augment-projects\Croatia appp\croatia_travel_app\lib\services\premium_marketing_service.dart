import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/premium_marketing.dart';

/// 💎 PREMIUM MARKETING SERVICE - Analytics jako key differentiator
class PremiumMarketingService {
  static final PremiumMarketingService _instance = PremiumMarketingService._internal();
  factory PremiumMarketingService() => _instance;
  PremiumMarketingService._internal();

  bool _isInitialized = false;
  final List<MarketingCampaign> _campaigns = [];
  final List<PremiumFeature> _premiumFeatures = [];
  final List<ConversionFunnel> _conversionFunnels = [];
  final Map<String, UserSegment> _userSegments = {};
  final StreamController<MarketingEvent> _eventController = StreamController.broadcast();

  /// Stream marketingových událostí
  Stream<MarketingEvent> get marketingEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('💎 Inicializuji Premium Marketing Service...');
      
      await _loadPremiumFeatures();
      await _loadUserSegments();
      await _initializeConversionFunnels();
      await _startMarketingCampaigns();
      
      _isInitialized = true;
      debugPrint('✅ Premium Marketing Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Premium Marketing: $e');
      await _createDefaultFeatures();
      _isInitialized = true;
    }
  }

  /// Zobrazení premium features uživateli
  Future<PremiumOffer> showPremiumOffer({
    required String userId,
    required OfferTrigger trigger,
    Map<String, dynamic>? context,
  }) async {
    try {
      // Analýza uživatele pro personalizaci
      final userProfile = await _analyzeUserProfile(userId);
      final segment = _getUserSegment(userProfile);
      
      // Výběr nejvhodnějších features
      final recommendedFeatures = _selectFeaturesForUser(userProfile, segment);
      
      // Personalizace ceny
      final pricing = _calculatePersonalizedPricing(segment, trigger);
      
      // Vytvoření nabídky
      final offer = PremiumOffer(
        id: 'offer_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        trigger: trigger,
        segment: segment,
        features: recommendedFeatures,
        pricing: pricing,
        personalizedMessage: _generatePersonalizedMessage(userProfile, segment),
        urgencyMessage: _generateUrgencyMessage(trigger),
        socialProof: _getSocialProof(segment),
        validUntil: DateTime.now().add(pricing.validityPeriod),
        createdAt: DateTime.now(),
      );

      // Tracking události
      _eventController.add(MarketingEvent(
        type: MarketingEventType.offerShown,
        userId: userId,
        timestamp: DateTime.now(),
        data: {
          'trigger': trigger.name,
          'segment': segment.name,
          'features': recommendedFeatures.map((f) => f.id).toList(),
        },
      ));

      debugPrint('💎 Premium nabídka zobrazena: $userId (${segment.name})');
      return offer;
    } catch (e) {
      debugPrint('❌ Chyba při zobrazování premium nabídky: $e');
      return PremiumOffer.defaultOffer(userId);
    }
  }

  /// A/B test premium features
  Future<ABTestResult> runFeatureABTest({
    required String featureId,
    required List<String> variants,
    required double trafficSplit,
    required Duration testDuration,
  }) async {
    try {
      final test = ABTest(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        featureId: featureId,
        variants: variants,
        trafficSplit: trafficSplit,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(testDuration),
        isActive: true,
        results: {},
      );

      // Simulace A/B testu
      await Future.delayed(const Duration(seconds: 1));
      
      // Generování mock výsledků
      final results = <String, ABTestVariantResult>{};
      for (final variant in variants) {
        results[variant] = ABTestVariantResult(
          variant: variant,
          impressions: 1000 + Random().nextInt(500),
          conversions: 50 + Random().nextInt(30),
          revenue: 2500.0 + Random().nextDouble() * 1000,
          conversionRate: 0.05 + Random().nextDouble() * 0.03,
          significance: 0.95 + Random().nextDouble() * 0.05,
        );
      }

      final testResult = ABTestResult(
        test: test,
        results: results,
        winner: _determineWinner(results),
        confidence: 0.95,
        completedAt: DateTime.now(),
      );

      _eventController.add(MarketingEvent(
        type: MarketingEventType.abTestCompleted,
        timestamp: DateTime.now(),
        data: {
          'featureId': featureId,
          'winner': testResult.winner,
          'confidence': testResult.confidence,
        },
      ));

      debugPrint('🧪 A/B test dokončen: ${testResult.winner} vyhrál');
      return testResult;
    } catch (e) {
      debugPrint('❌ Chyba při A/B testu: $e');
      rethrow;
    }
  }

  /// Personalizovaná onboarding sekvence
  Future<OnboardingSequence> createPersonalizedOnboarding({
    required String userId,
    required UserProfile userProfile,
  }) async {
    try {
      final segment = _getUserSegment(userProfile);
      final steps = <OnboardingStep>[];

      // Základní kroky pro všechny
      steps.addAll([
        OnboardingStep(
          id: 'welcome',
          title: 'Vítejte v Croatia Travel App! 🇭🇷',
          description: 'Začněte svou cestovní cestu s námi',
          type: OnboardingStepType.welcome,
          duration: const Duration(seconds: 5),
          isRequired: true,
        ),
        OnboardingStep(
          id: 'first_entry',
          title: 'Napište svůj první zápis',
          description: 'Zachyťte svou první vzpomínku',
          type: OnboardingStepType.action,
          duration: const Duration(minutes: 3),
          isRequired: true,
        ),
      ]);

      // Personalizované kroky podle segmentu
      switch (segment) {
        case UserSegment.powerUser:
          steps.addAll([
            OnboardingStep(
              id: 'advanced_search',
              title: 'Objevte pokročilé vyhledávání 🔍',
              description: 'Najděte vzpomínky podle nálady, počasí a více',
              type: OnboardingStepType.feature,
              duration: const Duration(minutes: 2),
              premiumFeature: _premiumFeatures.firstWhere((f) => f.id == 'advanced_search'),
            ),
            OnboardingStep(
              id: 'analytics_preview',
              title: 'Vaše osobní analytika 📊',
              description: 'Sledujte své vzorce a pokrok',
              type: OnboardingStepType.premium,
              duration: const Duration(minutes: 2),
              premiumFeature: _premiumFeatures.firstWhere((f) => f.id == 'personal_analytics'),
            ),
          ]);
          break;
        
        case UserSegment.familyOriented:
          steps.addAll([
            OnboardingStep(
              id: 'family_sharing',
              title: 'Sdílejte s rodinou 👨‍👩‍👧‍👦',
              description: 'Vytvořte rodinný kruh pro sdílení vzpomínek',
              type: OnboardingStepType.feature,
              duration: const Duration(minutes: 3),
              premiumFeature: _premiumFeatures.firstWhere((f) => f.id == 'family_sharing'),
            ),
          ]);
          break;
        
        case UserSegment.contentCreator:
          steps.addAll([
            OnboardingStep(
              id: 'media_features',
              title: 'Pokročilé media funkce 🎬',
              description: 'Time-lapse, filtry a kolláže',
              type: OnboardingStepType.feature,
              duration: const Duration(minutes: 2),
              premiumFeature: _premiumFeatures.firstWhere((f) => f.id == 'advanced_media'),
            ),
          ]);
          break;
        
        default:
          // Základní premium preview
          steps.add(OnboardingStep(
            id: 'premium_preview',
            title: 'Objevte Premium funkce 💎',
            description: 'Odemkněte plný potenciál aplikace',
            type: OnboardingStepType.premium,
            duration: const Duration(minutes: 1),
          ));
      }

      final sequence = OnboardingSequence(
        id: 'onboarding_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        segment: segment,
        steps: steps,
        currentStepIndex: 0,
        isCompleted: false,
        startedAt: DateTime.now(),
      );

      _eventController.add(MarketingEvent(
        type: MarketingEventType.onboardingStarted,
        userId: userId,
        timestamp: DateTime.now(),
        data: {'segment': segment.name, 'stepsCount': steps.length},
      ));

      debugPrint('🎯 Personalizovaný onboarding vytvořen: $userId');
      return sequence;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření onboardingu: $e');
      return OnboardingSequence.defaultSequence(userId);
    }
  }

  /// Retence kampaň pro premium conversion
  Future<RetentionCampaign> createRetentionCampaign({
    required UserSegment targetSegment,
    required RetentionGoal goal,
    required Duration duration,
  }) async {
    try {
      final campaign = RetentionCampaign(
        id: 'retention_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Premium Conversion - ${targetSegment.displayName}',
        targetSegment: targetSegment,
        goal: goal,
        startDate: DateTime.now(),
        endDate: DateTime.now().add(duration),
        touchpoints: _generateTouchpoints(targetSegment, goal),
        isActive: true,
      );

      _campaigns.add(MarketingCampaign(
        id: campaign.id,
        name: campaign.name,
        type: CampaignType.retention,
        targetSegment: targetSegment,
        startDate: campaign.startDate,
        endDate: campaign.endDate,
        budget: 5000.0,
        isActive: true,
        metrics: CampaignMetrics.empty(),
      ));

      _eventController.add(MarketingEvent(
        type: MarketingEventType.campaignStarted,
        timestamp: DateTime.now(),
        data: {
          'campaignId': campaign.id,
          'segment': targetSegment.name,
          'goal': goal.name,
        },
      ));

      debugPrint('🎯 Retence kampaň spuštěna: ${campaign.name}');
      return campaign;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření retence kampaně: $e');
      rethrow;
    }
  }

  /// Analýza conversion funnelu
  Future<FunnelAnalysis> analyzeConversionFunnel({
    required String funnelId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final funnel = _conversionFunnels.firstWhere((f) => f.id == funnelId);
      
      // Simulace analýzy dat
      final analysis = FunnelAnalysis(
        funnelId: funnelId,
        totalUsers: 10000,
        stepConversions: _generateStepConversions(funnel.steps),
        dropoffPoints: _identifyDropoffPoints(funnel.steps),
        averageTimeToConvert: const Duration(days: 7),
        topConversionPaths: _getTopConversionPaths(),
        segmentPerformance: _getSegmentPerformance(),
        recommendations: _generateFunnelRecommendations(),
        analyzedAt: DateTime.now(),
      );

      debugPrint('📊 Funnel analýza dokončena: $funnelId');
      return analysis;
    } catch (e) {
      debugPrint('❌ Chyba při analýze funnelu: $e');
      rethrow;
    }
  }

  /// Personalizace podle user profilu
  Future<UserProfile> _analyzeUserProfile(String userId) async {
    // Simulace analýzy uživatelského profilu
    return UserProfile(
      userId: userId,
      registrationDate: DateTime.now().subtract(Duration(days: Random().nextInt(365))),
      totalEntries: Random().nextInt(100),
      averageSessionDuration: Duration(minutes: 5 + Random().nextInt(20)),
      favoriteFeatures: ['diary_writing', 'photo_upload'],
      engagementScore: 0.3 + Random().nextDouble() * 0.7,
      premiumInterest: Random().nextDouble(),
      deviceType: ['iOS', 'Android', 'Web'][Random().nextInt(3)],
      location: 'Czech Republic',
    );
  }

  UserSegment _getUserSegment(UserProfile profile) {
    if (profile.totalEntries > 50 && profile.engagementScore > 0.8) {
      return UserSegment.powerUser;
    } else if (profile.favoriteFeatures.contains('family_sharing')) {
      return UserSegment.familyOriented;
    } else if (profile.favoriteFeatures.contains('photo_upload')) {
      return UserSegment.contentCreator;
    } else if (profile.premiumInterest > 0.7) {
      return UserSegment.premiumProspect;
    } else {
      return UserSegment.casual;
    }
  }

  List<PremiumFeature> _selectFeaturesForUser(UserProfile profile, UserSegment segment) {
    final features = <PremiumFeature>[];
    
    // Vždy zahrnout analytics jako key differentiator
    features.add(_premiumFeatures.firstWhere((f) => f.id == 'personal_analytics'));
    
    switch (segment) {
      case UserSegment.powerUser:
        features.addAll([
          _premiumFeatures.firstWhere((f) => f.id == 'advanced_search'),
          _premiumFeatures.firstWhere((f) => f.id == 'ai_suggestions'),
        ]);
        break;
      case UserSegment.familyOriented:
        features.add(_premiumFeatures.firstWhere((f) => f.id == 'family_sharing'));
        break;
      case UserSegment.contentCreator:
        features.add(_premiumFeatures.firstWhere((f) => f.id == 'advanced_media'));
        break;
      default:
        features.add(_premiumFeatures.firstWhere((f) => f.id == 'croatian_intelligence'));
    }
    
    return features.take(3).toList(); // Max 3 features pro focus
  }

  PremiumPricing _calculatePersonalizedPricing(UserSegment segment, OfferTrigger trigger) {
    double basePrice = 9.99; // Base monthly price
    double discount = 0.0;
    Duration validityPeriod = const Duration(days: 7);

    // Segment-based pricing
    switch (segment) {
      case UserSegment.powerUser:
        basePrice = 12.99; // Higher value perception
        break;
      case UserSegment.premiumProspect:
        discount = 0.3; // 30% discount for interested users
        break;
      case UserSegment.casual:
        discount = 0.5; // 50% discount to convert casual users
        validityPeriod = const Duration(days: 3); // Shorter urgency
        break;
      default:
        discount = 0.2; // 20% standard discount
    }

    // Trigger-based adjustments
    switch (trigger) {
      case OfferTrigger.featureLimit:
        discount += 0.1; // Additional 10% for feature limits
        break;
      case OfferTrigger.onboarding:
        discount += 0.2; // 20% onboarding discount
        break;
      case OfferTrigger.retention:
        discount += 0.15; // 15% retention discount
        break;
      default:
        break;
    }

    final finalPrice = basePrice * (1 - discount.clamp(0.0, 0.6)); // Max 60% discount

    return PremiumPricing(
      monthlyPrice: finalPrice,
      originalPrice: basePrice,
      discount: discount,
      validityPeriod: validityPeriod,
      paymentOptions: ['monthly', 'yearly'],
      trialPeriod: const Duration(days: 7),
    );
  }

  String _generatePersonalizedMessage(UserProfile profile, UserSegment segment) {
    switch (segment) {
      case UserSegment.powerUser:
        return 'Jako aktivní uživatel si zasloužíte pokročilé funkce! 🚀';
      case UserSegment.familyOriented:
        return 'Sdílejte své vzpomínky s celou rodinou! 👨‍👩‍👧‍👦';
      case UserSegment.contentCreator:
        return 'Vytvářejte úžasný obsah s profesionálními nástroji! 🎬';
      case UserSegment.premiumProspect:
        return 'Odemkněte plný potenciál své cestovní aplikace! 💎';
      default:
        return 'Objevte, co všechno dokážete s Premium! ✨';
    }
  }

  String _generateUrgencyMessage(OfferTrigger trigger) {
    switch (trigger) {
      case OfferTrigger.featureLimit:
        return 'Dosáhli jste limitu! Pokračujte bez omezení.';
      case OfferTrigger.onboarding:
        return 'Speciální uvítací nabídka - pouze dnes!';
      case OfferTrigger.retention:
        return 'Vracíme vás zpět se speciální slevou!';
      case OfferTrigger.seasonal:
        return 'Letní nabídka - ušetřete až 50%!';
      default:
        return 'Omezená nabídka - neváhejte!';
    }
  }

  List<SocialProof> _getSocialProof(UserSegment segment) {
    return [
      SocialProof(
        type: SocialProofType.userCount,
        message: 'Více než 15,000 uživatelů již používá Premium',
        value: '15,000+',
      ),
      SocialProof(
        type: SocialProofType.rating,
        message: 'Hodnocení 4.8/5 v App Store',
        value: '4.8',
      ),
      SocialProof(
        type: SocialProofType.testimonial,
        message: '"Nejlepší cestovní deník, jaký jsem kdy používal!" - Jana K.',
        value: 'testimonial',
      ),
    ];
  }

  List<TouchPoint> _generateTouchpoints(UserSegment segment, RetentionGoal goal) {
    return [
      TouchPoint(
        day: 1,
        channel: TouchPointChannel.inApp,
        message: 'Vítejte zpět! Podívejte se na nové funkce.',
        action: TouchPointAction.showFeatures,
      ),
      TouchPoint(
        day: 3,
        channel: TouchPointChannel.email,
        message: 'Vaše vzpomínky čekají na analýzu',
        action: TouchPointAction.showAnalytics,
      ),
      TouchPoint(
        day: 7,
        channel: TouchPointChannel.push,
        message: 'Speciální nabídka Premium - 30% sleva!',
        action: TouchPointAction.showOffer,
      ),
    ];
  }

  /// Pomocné metody pro analýzy
  List<StepConversion> _generateStepConversions(List<String> steps) {
    final conversions = <StepConversion>[];
    double conversionRate = 1.0;
    
    for (int i = 0; i < steps.length; i++) {
      conversionRate *= (0.7 + Random().nextDouble() * 0.25); // 70-95% conversion
      conversions.add(StepConversion(
        step: steps[i],
        users: (10000 * conversionRate).round(),
        conversionRate: i == 0 ? 1.0 : conversionRate / conversions[i-1].conversionRate,
      ));
    }
    
    return conversions;
  }

  List<String> _identifyDropoffPoints(List<String> steps) {
    return steps.where((step) => Random().nextBool()).take(2).toList();
  }

  List<String> _getTopConversionPaths() {
    return [
      'Onboarding → First Entry → Premium Trial → Subscription',
      'Feature Limit → Premium Offer → Trial → Subscription',
      'Analytics Preview → Premium Features → Subscription',
    ];
  }

  Map<String, double> _getSegmentPerformance() {
    return {
      'Power Users': 0.25 + Random().nextDouble() * 0.15,
      'Family Oriented': 0.18 + Random().nextDouble() * 0.12,
      'Content Creators': 0.22 + Random().nextDouble() * 0.13,
      'Casual Users': 0.08 + Random().nextDouble() * 0.07,
    };
  }

  List<String> _generateFunnelRecommendations() {
    return [
      'Optimalizujte onboarding pro lepší first-time experience',
      'Přidejte více social proof elementů',
      'Zkraťte cestu k premium features',
      'Implementujte exit-intent popups',
      'Personalizujte nabídky podle user segmentů',
    ];
  }

  String _determineWinner(Map<String, ABTestVariantResult> results) {
    return results.entries
        .reduce((a, b) => a.value.conversionRate > b.value.conversionRate ? a : b)
        .key;
  }

  /// Načítání a ukládání dat
  Future<void> _loadPremiumFeatures() async {
    await _createDefaultFeatures();
  }

  Future<void> _loadUserSegments() async {
    // Načtení user segmentů
  }

  Future<void> _initializeConversionFunnels() async {
    _conversionFunnels.addAll([
      ConversionFunnel(
        id: 'premium_conversion',
        name: 'Premium Conversion Funnel',
        steps: [
          'App Download',
          'Registration',
          'First Entry',
          'Feature Discovery',
          'Premium Preview',
          'Trial Start',
          'Subscription',
        ],
      ),
    ]);
  }

  Future<void> _startMarketingCampaigns() async {
    // Spuštění automatických kampaní
  }

  Future<void> _createDefaultFeatures() async {
    _premiumFeatures.addAll([
      PremiumFeature(
        id: 'personal_analytics',
        name: 'Osobní analytika',
        description: 'Detailní insights o vašich vzorcích psaní a náladách',
        category: FeatureCategory.analytics,
        value: 'Objevte skryté vzorce ve vašich vzpomínkách',
        icon: '📊',
        isKeyDifferentiator: true,
      ),
      PremiumFeature(
        id: 'advanced_search',
        name: 'Pokročilé vyhledávání',
        description: 'Vyhledávání podle nálady, počasí, lokace a více',
        category: FeatureCategory.search,
        value: 'Najděte jakoukoliv vzpomínku během sekund',
        icon: '🔍',
        isKeyDifferentiator: true,
      ),
      PremiumFeature(
        id: 'family_sharing',
        name: 'Rodinné sdílení',
        description: 'Soukromé kruhy pro sdílení vzpomínek s rodinou',
        category: FeatureCategory.social,
        value: 'Spojte rodinu prostřednictvím vzpomínek',
        icon: '👨‍👩‍👧‍👦',
        isKeyDifferentiator: false,
      ),
      PremiumFeature(
        id: 'ai_suggestions',
        name: 'AI návrhy',
        description: 'Inteligentní návrhy pro lepší psaní',
        category: FeatureCategory.ai,
        value: 'Pište lepší příběhy s pomocí AI',
        icon: '🤖',
        isKeyDifferentiator: true,
      ),
      PremiumFeature(
        id: 'advanced_media',
        name: 'Pokročilé media',
        description: 'Time-lapse, filtry, kolláže a více',
        category: FeatureCategory.media,
        value: 'Vytvářejte profesionální obsah',
        icon: '🎬',
        isKeyDifferentiator: false,
      ),
      PremiumFeature(
        id: 'croatian_intelligence',
        name: 'Croatian Intelligence',
        description: 'Lokální informace, události a tipy',
        category: FeatureCategory.local,
        value: 'Objevte Chorvatsko jako místní',
        icon: '🇭🇷',
        isKeyDifferentiator: true,
      ),
    ]);
  }

  @override
  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<PremiumFeature> get premiumFeatures => List.unmodifiable(_premiumFeatures);
  List<MarketingCampaign> get campaigns => List.unmodifiable(_campaigns);
}
