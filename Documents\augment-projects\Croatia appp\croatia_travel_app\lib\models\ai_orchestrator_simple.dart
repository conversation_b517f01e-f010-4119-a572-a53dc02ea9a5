/// Kontextové informace pro AI
class AIContext {
  final String userId;
  final LocationData? currentLocation;
  final UserProfile? userProfile;
  final List<String> recentQueries;
  final Map<String, dynamic> sessionData;
  final DateTime timestamp;
  final String language;
  final ConversationMemory? memory;

  const AIContext({
    required this.userId,
    this.currentLocation,
    this.userProfile,
    this.recentQueries = const [],
    this.sessionData = const {},
    required this.timestamp,
    this.language = 'cs',
    this.memory,
  });

  AIContext copyWith({
    String? userId,
    LocationData? currentLocation,
    UserProfile? userProfile,
    List<String>? recentQueries,
    Map<String, dynamic>? sessionData,
    DateTime? timestamp,
    String? language,
    ConversationMemory? memory,
  }) {
    return AIContext(
      userId: userId ?? this.userId,
      currentLocation: currentLocation ?? this.currentLocation,
      userProfile: userProfile ?? this.userProfile,
      recentQueries: recentQueries ?? this.recentQueries,
      sessionData: sessionData ?? this.sessionData,
      timestamp: timestamp ?? this.timestamp,
      language: language ?? this.language,
      memory: memory ?? this.memory,
    );
  }
}

/// Lokační data
class LocationData {
  final double latitude;
  final double longitude;
  final String? city;
  final String? country;
  final String? address;
  final double? accuracy;
  final DateTime timestamp;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.city,
    this.country,
    this.address,
    this.accuracy,
    required this.timestamp,
  });
}

/// Uživatelský profil
class UserProfile {
  final String id;
  final String name;
  final List<String> interests;
  final Map<String, dynamic> preferences;
  final List<String> visitedPlaces;
  final String preferredLanguage;
  final TravelStyle travelStyle;
  final Budget budget;

  const UserProfile({
    required this.id,
    required this.name,
    this.interests = const [],
    this.preferences = const {},
    this.visitedPlaces = const [],
    this.preferredLanguage = 'cs',
    this.travelStyle = TravelStyle.balanced,
    this.budget = Budget.medium,
  });
}

/// Styl cestování
enum TravelStyle {
  luxury,
  budget,
  adventure,
  cultural,
  relaxed,
  family,
  romantic,
  business,
  balanced,
}

/// Rozpočet
enum Budget {
  low,
  medium,
  high,
  unlimited,
}

/// Paměť konverzace
class ConversationMemory {
  final Map<String, dynamic> facts;
  final List<String> topics;
  final Map<String, int> preferences;
  final List<UserInteraction> interactions;
  final DateTime lastUpdated;

  const ConversationMemory({
    this.facts = const {},
    this.topics = const [],
    this.preferences = const {},
    this.interactions = const [],
    required this.lastUpdated,
  });

  ConversationMemory addFact(String key, dynamic value) {
    final newFacts = Map<String, dynamic>.from(facts);
    newFacts[key] = value;
    return ConversationMemory(
      facts: newFacts,
      topics: topics,
      preferences: preferences,
      interactions: interactions,
      lastUpdated: DateTime.now(),
    );
  }

  ConversationMemory addInteraction(UserInteraction interaction) {
    final newInteractions = List<UserInteraction>.from(interactions);
    newInteractions.add(interaction);
    return ConversationMemory(
      facts: facts,
      topics: topics,
      preferences: preferences,
      interactions: newInteractions,
      lastUpdated: DateTime.now(),
    );
  }
}

/// Uživatelská interakce
class UserInteraction {
  final String query;
  final String response;
  final bool wasHelpful;
  final double confidence;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const UserInteraction({
    required this.query,
    required this.response,
    required this.wasHelpful,
    required this.confidence,
    required this.timestamp,
    this.metadata = const {},
  });
}

/// Odpověď AI systému
class AIResponse {
  final String content;
  final AIResponseType type;
  final double confidence;
  final AIResponseSource source;
  final List<String> suggestedActions;
  final Map<String, dynamic> metadata;
  final List<AIRecommendation> recommendations;
  final DateTime timestamp;

  const AIResponse({
    required this.content,
    required this.type,
    required this.confidence,
    required this.source,
    this.suggestedActions = const [],
    this.metadata = const {},
    this.recommendations = const [],
    required this.timestamp,
  });
}

/// Typ odpovědi AI
enum AIResponseType {
  text,
  recommendation,
  action,
  error,
  clarification,
}

/// Zdroj odpovědi AI
enum AIResponseSource {
  local,
  cloud,
  hybrid,
  cache,
  fallback,
}

/// AI doporučení
class AIRecommendation {
  final String id;
  final String title;
  final String description;
  final RecommendationType type;
  final double score;
  final Map<String, dynamic> data;
  final String? imageUrl;
  final LocationData? location;

  const AIRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.score,
    this.data = const {},
    this.imageUrl,
    this.location,
  });
}

/// Typ doporučení
enum RecommendationType {
  restaurant,
  attraction,
  accommodation,
  activity,
  transport,
  event,
  route,
}

/// Výsledek učení AI
class LearningResult {
  final String userId;
  final Map<String, double> updatedPreferences;
  final List<String> newInterests;
  final double confidenceImprovement;
  final DateTime timestamp;

  const LearningResult({
    required this.userId,
    this.updatedPreferences = const {},
    this.newInterests = const [],
    required this.confidenceImprovement,
    required this.timestamp,
  });
}
