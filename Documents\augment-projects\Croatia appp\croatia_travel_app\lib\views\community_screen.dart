import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'neighborhood_help_screen.dart';

/// 🤝 COMMUNITY SCREEN - Zajednica
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF006994), // Adriatic Blue
              Color(0xFF2E8B8B), // Teal
              Color(0xFFFF6B35), // Sunset Orange
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: CustomPaint(
          painter: WatercolorCommunityBackgroundPainter(),
          child: <PERSON><PERSON><PERSON>(
            child: <PERSON>um<PERSON>(
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Text(
                        '🤝 Zajednica',
                        style: GoogleFonts.inter(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ).animate().fadeIn().slideY(begin: -0.3),
                      const SizedBox(height: 8),
                      Text(
                        'Povezujemo ljude u Hrvatskoj',
                        style: GoogleFonts.inter(
                          fontSize: 16,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ).animate().fadeIn(delay: 200.ms).slideY(begin: -0.3),
                    ],
                  ),
                ),

                // Main content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: [
                        // Community stats
                        _buildCommunityStats(),

                        const SizedBox(height: 24),

                        // Main features
                        _buildMainFeatures(),

                        const SizedBox(height: 24),

                        // Recent activity
                        _buildRecentActivity(),

                        const SizedBox(height: 24),

                        // Local groups
                        _buildLocalGroups(),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCommunityStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.2),
            Colors.white.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            '📊 Naša zajednica',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildStatItem('👥', '12,847', 'Članova')),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              Expanded(child: _buildStatItem('🤝', '3,241', 'Pomoći')),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              Expanded(child: _buildStatItem('🏙️', '47', 'Gradova')),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(delay: 300.ms).slideY(begin: 0.3);
  }

  Widget _buildStatItem(String icon, String number, String label) {
    return Column(
      children: [
        Text(icon, style: const TextStyle(fontSize: 24)),
        const SizedBox(height: 8),
        Text(
          number,
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 12,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildMainFeatures() {
    final features = [
      {
        'icon': '🤝',
        'title': 'Susjedska pomoć',
        'subtitle': 'Ponuda i potražnja usluga',
        'color': const Color(0xFF006994),
        'action': () => _openNeighborhoodHelp(),
      },
      {
        'icon': '💬',
        'title': 'Lokalni chat',
        'subtitle': 'Razgovaraj sa susjedima',
        'color': const Color(0xFF2E8B8B),
        'action': () => _showComingSoon('Lokalni chat'),
      },
      {
        'icon': '📅',
        'title': 'Događaji u susjedstvu',
        'subtitle': 'Organiziraj i pridruži se',
        'color': const Color(0xFFFF6B35),
        'action': () => _showComingSoon('Događaji'),
      },
      {
        'icon': '🏪',
        'title': 'Lokalni marketplace',
        'subtitle': 'Kupuj i prodavaj lokalno',
        'color': const Color(0xFF8FBC8F),
        'action': () => _showComingSoon('Marketplace'),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🌟 Glavne funkcije',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ).animate().fadeIn(delay: 500.ms),
        const SizedBox(height: 16),
        ...features.asMap().entries.map((entry) {
          final index = entry.key;
          final feature = entry.value;

          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: feature['action'] as VoidCallback,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: (feature['color'] as Color).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            feature['icon'] as String,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              feature['title'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              feature['subtitle'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white.withOpacity(0.5),
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ).animate().fadeIn(delay: (600 + index * 100).ms).slideX(begin: 0.3);
        }).toList(),
      ],
    );
  }

  Widget _buildRecentActivity() {
    final activities = [
      {
        'icon': '🔧',
        'title': 'Marko traži majstora',
        'subtitle': 'Popravak grijanja u Splitu',
        'time': 'Prije 15 min',
        'type': 'request',
      },
      {
        'icon': '🌿',
        'title': 'Ana nudi usluge',
        'subtitle': 'Održavanje vrta i okućnice',
        'time': 'Prije 1 sat',
        'type': 'offer',
      },
      {
        'icon': '🎉',
        'title': 'Novi član se pridružio',
        'subtitle': 'Petra iz Zagreba',
        'time': 'Prije 2 sata',
        'type': 'join',
      },
      {
        'icon': '✅',
        'title': 'Uspješno riješeno',
        'subtitle': 'Prijevoz namještaja u Rijeci',
        'time': 'Prije 3 sata',
        'type': 'completed',
      },
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📈 Nedavna aktivnost',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          ...activities.asMap().entries.map((entry) {
            final index = entry.key;
            final activity = entry.value;

            return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: _getActivityColor(
                            activity['type'] as String,
                          ).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Center(
                          child: Text(
                            activity['icon'] as String,
                            style: const TextStyle(fontSize: 18),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              activity['title'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              activity['subtitle'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        activity['time'] as String,
                        style: GoogleFonts.inter(
                          fontSize: 11,
                          color: Colors.white.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                )
                .animate()
                .fadeIn(delay: (1000 + index * 100).ms)
                .slideX(begin: 0.3);
          }).toList(),
        ],
      ),
    ).animate().fadeIn(delay: 1000.ms).slideY(begin: 0.3);
  }

  Widget _buildLocalGroups() {
    final groups = [
      {'name': 'Split - Centar', 'members': '2,847', 'icon': '🏛️'},
      {'name': 'Zagreb - Novi Zagreb', 'members': '1,923', 'icon': '🏢'},
      {'name': 'Rijeka - Centar', 'members': '1,456', 'icon': '⚓'},
      {'name': 'Dubrovnik - Stari Grad', 'members': '987', 'icon': '🏰'},
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.15),
            Colors.white.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🏙️ Lokalne grupe',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          ...groups.asMap().entries.map((entry) {
            final index = entry.key;
            final group = entry.value;

            return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () => _showComingSoon('Lokalne grupe'),
                      child: Row(
                        children: [
                          Text(
                            group['icon'] as String,
                            style: const TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              group['name'] as String,
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Text(
                            '${group['members']} članova',
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.white.withOpacity(0.5),
                            size: 14,
                          ),
                        ],
                      ),
                    ),
                  ),
                )
                .animate()
                .fadeIn(delay: (1300 + index * 100).ms)
                .slideX(begin: 0.3);
          }).toList(),
        ],
      ),
    ).animate().fadeIn(delay: 1300.ms).slideY(begin: 0.3);
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'request':
        return Colors.blue;
      case 'offer':
        return Colors.green;
      case 'join':
        return Colors.purple;
      case 'completed':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  void _openNeighborhoodHelp() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NeighborhoodHelpScreen()),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '$feature uskoro dostupan! 🚀',
          style: GoogleFonts.inter(),
        ),
        backgroundColor: const Color(0xFF006994),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}

/// Custom painter pro watercolor pozadí
class WatercolorCommunityBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor spots
    final spots = [
      {
        'x': size.width * 0.1,
        'y': size.height * 0.2,
        'radius': 80.0,
        'color': Colors.white.withOpacity(0.08),
      },
      {
        'x': size.width * 0.9,
        'y': size.height * 0.35,
        'radius': 70.0,
        'color': Colors.white.withOpacity(0.06),
      },
      {
        'x': size.width * 0.3,
        'y': size.height * 0.8,
        'radius': 100.0,
        'color': Colors.white.withOpacity(0.05),
      },
      {
        'x': size.width * 0.8,
        'y': size.height * 0.9,
        'radius': 60.0,
        'color': Colors.white.withOpacity(0.08),
      },
    ];

    for (final spot in spots) {
      paint.color = spot['color'] as Color;
      canvas.drawCircle(
        Offset(spot['x'] as double, spot['y'] as double),
        spot['radius'] as double,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
